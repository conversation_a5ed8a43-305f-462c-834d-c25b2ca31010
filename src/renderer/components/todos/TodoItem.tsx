import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Check,
  Flag,
  Calendar,
  Edit3,
  Trash2,
  Clock,
  AlertCircle,
  Tag,
  MoreHorizontal
} from 'lucide-react';
import { Todo, TodoStatus, TodoPriority } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';
import { ConfirmationDialog } from '@renderer/components/ui/ConfirmationDialog';

interface TodoItemProps {
  todo: Todo;
  onUpdate?: (updatedTodo: Todo) => void;
  onDelete?: (todoId: string) => void;
}

export const TodoItem: React.FC<TodoItemProps> = ({ todo, onUpdate, onDelete }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(todo.title);
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const isCompleted = todo.status === 'completed';
  const isOverdue = todoService.isOverdue(todo);

  const handleToggleComplete = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const updatedTodo = await todoService.toggleTodoCompletion(todo.id, todo.status);
      onUpdate?.(updatedTodo);
    } catch (error) {
      console.error('Failed to toggle todo completion:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, todo.status, isLoading, onUpdate]);

  const handleDelete = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      await todoService.deleteTodo(todo.id);
      onDelete?.(todo.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Failed to delete todo:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, isLoading, onDelete]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = useCallback(async () => {
    if (isLoading || editValue.trim() === todo.title) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    try {
      const updatedTodo = await todoService.updateTodo(todo.id, { title: editValue.trim() });
      onUpdate?.(updatedTodo);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update todo:', error);
      // TODO: Show error toast
      setEditValue(todo.title); // Reset to original value
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, todo.title, editValue, isLoading, onUpdate]);

  const handleCancelEdit = () => {
    setEditValue(todo.title);
    setIsEditing(false);
  };

  const getPriorityColor = () => {
    switch (todo.priority) {
      case 'very_high': return 'text-fa-error';
      case 'high': return 'text-red-500';
      case 'medium': return 'text-fa-warning';
      case 'low': return 'text-fa-success';
      case 'very_low': return 'text-fa-gray-400';
      default: return 'text-fa-gray-400';
    }
  };

  const getPriorityIcon = () => {
    switch (todo.priority) {
      case 'very_high':
      case 'high':
        return <Flag className={`w-4 h-4 ${getPriorityColor()}`} />;
      case 'medium':
        return <Flag className={`w-4 h-4 ${getPriorityColor()}`} />;
      case 'low':
      case 'very_low':
        return <Flag className={`w-4 h-4 ${getPriorityColor()}`} />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (todo.status) {
      case 'completed': return 'text-fa-success';
      case 'in_progress': return 'text-fa-info';
      case 'pending': return 'text-fa-gray-500';
      case 'cancelled': return 'text-fa-error';
      case 'archived': return 'text-fa-gray-400';
      default: return 'text-fa-gray-500';
    }
  };

  return (
    <>
      <div
        className={`fa-todo-card relative transition-all duration-300 ${
          isCompleted ? 'opacity-70' : ''
        } ${isOverdue ? 'border-l-4 border-fa-error' : ''}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-start">
          {/* Checkbox */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleToggleComplete}
            disabled={isLoading}
            className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 transition-all duration-200 ${
              isCompleted
                ? 'bg-fa-success border-fa-success text-white'
                : 'border-fa-gray-300 hover:border-fa-blue-400'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? (
              <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              isCompleted && <Check className="w-4 h-4" />
            )}
          </motion.button>

        {/* Todo Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-2">
              <input
                type="text"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveEdit();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
                className="w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1"
                autoFocus
                disabled={isLoading}
              />
              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSaveEdit}
                  disabled={isLoading}
                  className="fa-button-glass text-xs px-2 py-1"
                >
                  Save
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleCancelEdit}
                  disabled={isLoading}
                  className="fa-button-glass text-xs px-2 py-1"
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-start justify-between">
                <h3 className={`text-lg font-medium flex-1 ${
                  isCompleted
                    ? 'line-through text-fa-gray-500'
                    : 'text-fa-gray-800'
                }`}>
                  {todo.title}
                </h3>
                {isOverdue && (
                  <div className="flex items-center text-fa-error ml-2">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    <span className="fa-caption">Overdue</span>
                  </div>
                )}
              </div>

              {todo.description && (
                <p className="fa-body text-fa-gray-600 mt-1 line-clamp-2">
                  {todo.description}
                </p>
              )}

              <div className="flex items-center space-x-4 mt-2 flex-wrap gap-2">
                {/* Status Badge */}
                <span className={`fa-caption px-2 py-1 rounded-full bg-fa-white-glass ${getStatusColor()}`}>
                  {todo.status?.replace('_', ' ') || 'pending'}
                </span>

                {/* Tags */}
                {todo.tags && todo.tags.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <Tag className="w-3 h-3 text-fa-gray-400" />
                    {todo.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="fa-caption bg-fa-blue-100 text-fa-blue-700 px-2 py-1 rounded-full">
                        {tag}
                      </span>
                    ))}
                    {todo.tags.length > 3 && (
                      <span className="fa-caption text-fa-gray-400">+{todo.tags.length - 3}</span>
                    )}
                  </div>
                )}

                {/* Due Date */}
                {todo.due_date && (
                  <div className={`flex items-center ${isOverdue ? 'text-fa-error' : 'text-fa-gray-500'}`}>
                    <Calendar className="w-4 h-4 mr-1" />
                    <span className="fa-caption">{todoService.formatDueDate(todo.due_date)}</span>
                  </div>
                )}

                {/* Priority */}
                <div className="flex items-center">
                  {getPriorityIcon()}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        {!isEditing && (
          <motion.div
            className="flex items-center space-x-1 ml-2"
            initial={{ opacity: 0, width: 0 }}
            animate={{
              opacity: isHovered ? 1 : 0,
              width: isHovered ? 'auto' : 0
            }}
            transition={{ duration: 0.2 }}
          >
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleEdit}
              disabled={isLoading}
              className="p-1 text-fa-gray-400 hover:text-fa-blue-500 disabled:opacity-50"
              title="Edit todo"
            >
              <Edit3 className="w-4 h-4" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowDeleteDialog(true)}
              disabled={isLoading}
              className="p-1 text-fa-gray-400 hover:text-fa-error disabled:opacity-50"
              title="Delete todo"
            >
              <Trash2 className="w-4 h-4" />
            </motion.button>
          </motion.div>
        )}
      </div>

      {/* Priority indicator */}
      {(todo.priority === 'high' || todo.priority === 'very_high') && (
        <div className="absolute top-0 left-0 w-full h-1 bg-fa-error rounded-t-2xl"></div>
      )}
    </div>

    {/* Delete Confirmation Dialog */}
    <ConfirmationDialog
      isOpen={showDeleteDialog}
      onClose={() => setShowDeleteDialog(false)}
      onConfirm={handleDelete}
      title="Delete Todo"
      message={`Are you sure you want to delete "${todo.title}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      variant="danger"
      isLoading={isLoading}
    />
  </>
  );
};