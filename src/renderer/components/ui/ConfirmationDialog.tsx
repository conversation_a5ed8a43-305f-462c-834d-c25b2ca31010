import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X } from 'lucide-react';

export interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info';
  isLoading?: boolean;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'danger',
  isLoading = false,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return {
          icon: 'text-fa-error',
          confirmButton: 'bg-fa-error hover:bg-red-600 text-white',
          border: 'border-fa-error-border',
        };
      case 'warning':
        return {
          icon: 'text-fa-warning',
          confirmButton: 'bg-fa-warning hover:bg-yellow-600 text-white',
          border: 'border-fa-warning-border',
        };
      case 'info':
        return {
          icon: 'text-fa-info',
          confirmButton: 'bg-fa-info hover:bg-blue-600 text-white',
          border: 'border-fa-info-border',
        };
      default:
        return {
          icon: 'text-fa-error',
          confirmButton: 'bg-fa-error hover:bg-red-600 text-white',
          border: 'border-fa-error-border',
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Dialog */}
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className={`fa-glass-panel-frosted max-w-md w-full p-6 ${styles.border}`}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full bg-fa-white-glass ${styles.icon}`}>
                    <AlertTriangle className="w-5 h-5" />
                  </div>
                  <h3 className="fa-heading-3 text-fa-gray-800">{title}</h3>
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-1 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                  disabled={isLoading}
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>

              {/* Message */}
              <div className="mb-6">
                <p className="fa-body text-fa-gray-600">{message}</p>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end space-x-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  disabled={isLoading}
                  className="fa-button-glass px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {cancelText}
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onConfirm}
                  disabled={isLoading}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${styles.confirmButton}`}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    confirmText
                  )}
                </motion.button>
              </div>
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  );
};

// Hook for easier usage
export const useConfirmationDialog = () => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [config, setConfig] = React.useState<Partial<ConfirmationDialogProps>>({});

  const showDialog = (dialogConfig: Partial<ConfirmationDialogProps>) => {
    setConfig(dialogConfig);
    setIsOpen(true);
  };

  const hideDialog = () => {
    setIsOpen(false);
    setConfig({});
  };

  const ConfirmationDialogComponent = React.useCallback(
    (props: Partial<ConfirmationDialogProps>) => (
      <ConfirmationDialog
        isOpen={isOpen}
        onClose={hideDialog}
        title=""
        message=""
        {...config}
        {...props}
      />
    ),
    [isOpen, config]
  );

  return {
    showDialog,
    hideDialog,
    ConfirmationDialog: ConfirmationDialogComponent,
    isOpen,
  };
};
