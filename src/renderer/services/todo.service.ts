import { Todo, TodoStatus, TodoPriority } from '@shared/types';

declare global {
  interface Window {
    electronAPI: {
      todos: {
        getAll: (sessionId: string, filters?: any, pagination?: any) => Promise<any>;
        create: (sessionId: string, todoData: any) => Promise<any>;
        update: (sessionId: string, todoId: string, updates: any) => Promise<any>;
        delete: (sessionId: string, todoId: string) => Promise<any>;
        updateStatus: (sessionId: string, todoId: string, status: string) => Promise<any>;
      };
    };
  }
}

export interface CreateTodoRequest {
  title: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

export interface TodoFilters {
  status?: TodoStatus;
  priority?: TodoPriority;
  category_id?: string;
  due_date_from?: Date;
  due_date_to?: Date;
  tags?: string[];
  search?: string;
  is_completed?: boolean;
  is_overdue?: boolean;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export class TodoService {
  private sessionId: string | null = null;

  constructor() {
    // For now, we'll use a mock session ID
    // In a real implementation, this would come from authentication
    this.sessionId = 'mock-session-id';
  }

  setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  private async makeRequest<T>(
    operation: string,
    ...args: any[]
  ): Promise<T> {
    if (!this.sessionId) {
      throw new Error('No active session');
    }

    if (!window.electronAPI?.todos) {
      throw new Error('Electron API not available');
    }

    const response: APIResponse<T> = await (window.electronAPI.todos as any)[operation](
      this.sessionId,
      ...args
    );

    if (!response.success) {
      throw new Error(response.error || 'Unknown error');
    }

    return response.data as T;
  }

  async getAllTodos(
    filters?: TodoFilters,
    pagination?: PaginationOptions
  ): Promise<{ data: Todo[]; total: number }> {
    return this.makeRequest('getAll', filters, pagination);
  }

  async createTodo(todoData: CreateTodoRequest): Promise<Todo> {
    return this.makeRequest('create', todoData);
  }

  async updateTodo(todoId: string, updates: UpdateTodoRequest): Promise<Todo> {
    return this.makeRequest('update', todoId, updates);
  }

  async deleteTodo(todoId: string): Promise<Todo> {
    return this.makeRequest('delete', todoId);
  }

  async updateTodoStatus(todoId: string, status: TodoStatus): Promise<Todo> {
    return this.makeRequest('updateStatus', todoId, status);
  }

  async toggleTodoCompletion(todoId: string, currentStatus: TodoStatus): Promise<Todo> {
    const newStatus: TodoStatus = currentStatus === 'completed' ? 'pending' : 'completed';
    return this.updateTodoStatus(todoId, newStatus);
  }

  // Utility methods
  isOverdue(todo: Todo): boolean {
    if (!todo.due_date) return false;
    return new Date(todo.due_date) < new Date() && todo.status !== 'completed';
  }

  getPriorityWeight(priority: TodoPriority): number {
    const weights = {
      very_low: 1,
      low: 2,
      medium: 3,
      high: 4,
      very_high: 5,
    };
    return weights[priority] || 3;
  }

  formatDueDate(date: Date | string): string {
    const dueDate = new Date(date);
    const now = new Date();
    const diffTime = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `${Math.abs(diffDays)} days overdue`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else if (diffDays <= 7) {
      return `Due in ${diffDays} days`;
    } else {
      return dueDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: dueDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    }
  }
}

export const todoService = new TodoService();
