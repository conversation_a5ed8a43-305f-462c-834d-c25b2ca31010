import { contextBridge, ipc<PERSON><PERSON>er } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Database operations
  database: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', sql, params),
    transaction: (operations: any[]) => ipcRenderer.invoke('db:transaction', operations),
  },

  // Security operations
  security: {
    encrypt: (data: string) => ipcRenderer.invoke('security:encrypt', data),
    decrypt: (encryptedData: string) => ipcRenderer.invoke('security:decrypt', encryptedData),
  },

  // MCP operations
  mcp: {
    sync: (data: any) => ipcRenderer.invoke('mcp:sync', data),
  },

  // Todo operations
  todos: {
    getAll: (sessionId: string, filters?: any, pagination?: any) =>
      ipcRenderer.invoke('todos:getAll', sessionId, filters, pagination),
    create: (sessionId: string, todoData: any) =>
      ipcRenderer.invoke('todos:create', sessionId, todoData),
    update: (sessionId: string, todoId: string, updates: any) =>
      ipcRenderer.invoke('todos:update', sessionId, todoId, updates),
    delete: (sessionId: string, todoId: string) =>
      ipcRenderer.invoke('todos:delete', sessionId, todoId),
    updateStatus: (sessionId: string, todoId: string, status: string) =>
      ipcRenderer.invoke('todos:updateStatus', sessionId, todoId, status),
  },

  // System operations
  system: {
    getInfo: () => ipcRenderer.invoke('system:getInfo'),
    quit: () => ipcRenderer.invoke('app:quit'),
    minimize: () => ipcRenderer.invoke('app:minimize'),
    maximize: () => ipcRenderer.invoke('app:maximize'),
    close: () => ipcRenderer.invoke('app:close'),
  },

  // Event listeners
  on: (channel: string, callback: (event: any, ...args: any[]) => void) => {
    const validChannels = [
      'menu-new-todo',
      'menu-import',
      'menu-export',
      'sync-status-changed',
      'theme-changed',
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  // Remove event listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Export the type for the exposed API
export type ElectronAPIType = typeof electronAPI;

// Declare global interface for TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}